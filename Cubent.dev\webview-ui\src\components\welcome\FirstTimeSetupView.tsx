import { useCallback, useState } from "react"
import { VSCodeButton } from "@vscode/webview-ui-toolkit/react"
import { Key } from "lucide-react"

import { useExtensionState } from "@src/context/ExtensionStateContext"
import { validateApiConfiguration } from "@src/utils/validate"
import { vscode } from "@src/utils/vscode"
import { useAppTranslation } from "@src/i18n/TranslationContext"

import { ApiKeyManagerContent } from "../settings/ApiKeyManagerPopup"
import { Tab, TabContent } from "../common/Tab"
import { SectionHeader } from "../settings/SectionHeader"
import { Section } from "../settings/Section"

import QaptHero from "./QaptHero"

const FirstTimeSetupView = () => {
	const { apiConfiguration, hiddenProfiles, currentApiConfigName } = useExtensionState()
	const { t } = useAppTranslation()
	const [isLoading, setIsLoading] = useState(false)

	const handleContinue = useCallback(async () => {
		console.log("Continue button clicked")
		setIsLoading(true)
		try {
			// Send message to extension to complete first-time setup
			console.log("Sending completeFirstTimeSetup message", vscode)
			console.log("vscode object:", vscode)
			const message = { type: "completeFirstTimeSetup" as const }
			console.log("Message to send:", message)
			vscode.postMessage(message)
			console.log("Message sent successfully")
		} catch (error) {
			console.error("Failed to complete setup:", error)
			setIsLoading(false)
		}
	}, [])

	const handleSkipSetup = useCallback(() => {
		console.log("Skip setup button clicked")
		// Send message to extension to skip setup for now
		console.log("Sending skipFirstTimeSetup message", vscode)
		console.log("vscode object:", vscode)
		const message = { type: "skipFirstTimeSetup" as const }
		console.log("Message to send:", message)
		vscode.postMessage(message)
		console.log("Message sent successfully")
	}, [])

	const handleApiConfigurationChange = useCallback((config: any) => {
		// Handle API configuration changes - save to provider settings
		// Use the same approach as the regular welcome screen

		// Validate the configuration before saving
		const error = config ? validateApiConfiguration(config) : undefined
		if (error) {
			console.error("API configuration validation error:", error)
			// Don't save invalid configurations
			return
		}

		vscode.postMessage({
			type: "upsertApiConfiguration",
			text: currentApiConfigName || "default", // Use current profile name or default
			apiConfiguration: config,
		})
	}, [currentApiConfigName])

	const handleHiddenProfilesChange = useCallback((profiles: string[]) => {
		// Handle hidden profiles changes - forward to extension
		vscode.postMessage({
			type: "setHiddenProfiles",
			profiles: profiles,
		})
	}, [])

	// Check if user has configured at least one API key - just check if any key exists
	const hasApiKey =
		apiConfiguration &&
		Object.values(apiConfiguration).some(
			(value) => typeof value === "string" && value.trim().length > 0 && value !== "-",
		)

	return (
		<Tab>
			<TabContent className="flex flex-col gap-5">
				<QaptHero />
				<div className="text-center -mt-4">
					<h2 className="text-xl font-bold mb-2">Let's get you started</h2>
					<p className="text-vscode-descriptionForeground">
						Let's get you set up with an AI model to start coding with Cubent.
					</p>
				</div>

				{/* API Key Manager without header and border */}
				<div>
					<ApiKeyManagerContent
						apiConfiguration={apiConfiguration}
						onApiConfigurationChange={handleApiConfigurationChange}
						hiddenProfiles={hiddenProfiles}
						onHiddenProfilesChange={handleHiddenProfilesChange}
						hiddenTabs={["builtin-models"]}
						centerTabs={true}
						hiddenProviders={["deepseek"]}
					/>
				</div>

				{/* Separator line */}
				<div className="w-full border-t border-vscode-foreground opacity-30 my-4"></div>

				<div className="p-4">
					<h4 className="font-semibold mb-2">What's Next?</h4>
					<ul className="text-sm text-vscode-descriptionForeground space-y-1">
						<li>• Add your API key for your preferred AI model</li>
						<li>• Start a conversation in the chat tab</li>
						<li>• Ask Cubent to help you write, debug, or explain code</li>
						<li>• Use @files to include specific files in your conversation</li>
					</ul>
				</div>
			</TabContent>

			<div className="sticky bottom-0 bg-vscode-sideBar-background p-3 border-t border-vscode-input-border">
				<div className="flex flex-col gap-2">
					<div className="flex justify-center">
						<VSCodeButton
							onClick={handleContinue}
							appearance="primary"
							disabled={isLoading}
							style={{
								width: "150px",
								borderRadius: "8px",
							}}>
							{isLoading ? "Setting up..." : "Continue"}
						</VSCodeButton>
					</div>

					<button
						onClick={handleSkipSetup}
						className="text-xs text-vscode-descriptionForeground hover:text-vscode-foreground cursor-pointer bg-transparent border-none p-1">
						I'll set up an API key later
					</button>

					{!hasApiKey && (
						<p className="text-xs text-vscode-descriptionForeground text-center">
							Add an API key above to enable the Continue button
						</p>
					)}
				</div>
			</div>
		</Tab>
	)
}

export default FirstTimeSetupView
